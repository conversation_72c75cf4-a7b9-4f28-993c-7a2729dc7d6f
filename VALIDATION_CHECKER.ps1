# Home Assistant Brands PR 验证脚本
# 验证所有文件是否符合 Home Assistant brands 仓库要求

Add-Type -AssemblyName System.Drawing

Write-Host "=== Home Assistant Brands PR 验证脚本 ===" -ForegroundColor Green
Write-Host ""

$validationResults = @()
$allPassed = $true

function Add-ValidationResult {
    param(
        [string]$Test,
        [bool]$Passed,
        [string]$Message
    )
    
    $result = [PSCustomObject]@{
        Test = $Test
        Passed = $Passed
        Message = $Message
    }
    
    $script:validationResults += $result
    
    if ($Passed) {
        Write-Host "✓ $Test" -ForegroundColor Green
        if ($Message) { Write-Host "  $Message" -ForegroundColor Gray }
    } else {
        Write-Host "✗ $Test" -ForegroundColor Red
        if ($Message) { Write-Host "  $Message" -ForegroundColor Yellow }
        $script:allPassed = $false
    }
}

function Test-ImageFile {
    param(
        [string]$Path,
        [int]$ExpectedWidth,
        [int]$ExpectedHeight,
        [string]$Description
    )
    
    if (-not (Test-Path $Path)) {
        Add-ValidationResult "$Description - 文件存在" $false "文件不存在: $Path"
        return
    }
    
    Add-ValidationResult "$Description - 文件存在" $true "文件路径: $Path"
    
    try {
        $img = [System.Drawing.Image]::FromFile($Path)
        
        # 检查尺寸
        $widthCorrect = $img.Width -eq $ExpectedWidth
        $heightCorrect = $img.Height -eq $ExpectedHeight
        
        Add-ValidationResult "$Description - 宽度 ($ExpectedWidth px)" $widthCorrect "实际宽度: $($img.Width) px"
        Add-ValidationResult "$Description - 高度 ($ExpectedHeight px)" $heightCorrect "实际高度: $($img.Height) px"
        
        # 检查格式
        $isPng = $img.RawFormat.Equals([System.Drawing.Imaging.ImageFormat]::Png)
        Add-ValidationResult "$Description - PNG 格式" $isPng "格式: $($img.RawFormat)"
        
        # 检查透明度支持
        $hasAlpha = $img.PixelFormat.ToString().Contains("Alpha") -or $img.PixelFormat.ToString().Contains("Argb")
        Add-ValidationResult "$Description - 透明度支持" $hasAlpha "像素格式: $($img.PixelFormat)"
        
        $img.Dispose()
        
        # 检查文件大小
        $fileInfo = Get-Item $Path
        $fileSizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
        $sizeOk = $fileSizeKB -lt 50
        Add-ValidationResult "$Description - 文件大小 (< 50KB)" $sizeOk "文件大小: $fileSizeKB KB"
        
    } catch {
        Add-ValidationResult "$Description - 图片读取" $false "无法读取图片: $($_.Exception.Message)"
    }
}

# 验证文件结构
Write-Host "1. 验证文件结构..." -ForegroundColor Cyan

$iconPath = "custom_integrations\ha_two_way_sync\icon.png"
$icon2xPath = "custom_integrations\ha_two_way_sync\<EMAIL>"

Test-ImageFile $iconPath 256 256 "icon.png (256x256)"
Test-ImageFile $icon2xPath 512 512 "<EMAIL> (512x512)"

# 验证目录结构
Write-Host "`n2. 验证目录结构..." -ForegroundColor Cyan

$dirExists = Test-Path "custom_integrations\ha_two_way_sync"
Add-ValidationResult "目录结构" $dirExists "custom_integrations/ha_two_way_sync/ 目录"

# 验证 PR 描述文件
Write-Host "`n3. 验证 PR 文档..." -ForegroundColor Cyan

$prDescExists = Test-Path "PR_DESCRIPTION.md"
Add-ValidationResult "PR 描述文档" $prDescExists "PR_DESCRIPTION.md 文件"

$manualGuideExists = Test-Path "MANUAL_SUBMIT_GUIDE.md"
Add-ValidationResult "手动提交指南" $manualGuideExists "MANUAL_SUBMIT_GUIDE.md 文件"

$forkGuideExists = Test-Path "FORK_AND_SETUP_GUIDE.md"
Add-ValidationResult "Fork 设置指南" $forkGuideExists "FORK_AND_SETUP_GUIDE.md 文件"

# 验证自动化脚本
Write-Host "`n4. 验证自动化脚本..." -ForegroundColor Cyan

$automationExists = Test-Path "SUBMIT_PR_AUTOMATION.ps1"
Add-ValidationResult "自动化提交脚本" $automationExists "SUBMIT_PR_AUTOMATION.ps1 文件"

# 显示总结
Write-Host "`n=== 验证总结 ===" -ForegroundColor Green

$passedCount = ($validationResults | Where-Object { $_.Passed }).Count
$totalCount = $validationResults.Count

Write-Host "通过: $passedCount / $totalCount" -ForegroundColor $(if ($allPassed) { "Green" } else { "Yellow" })

if ($allPassed) {
    Write-Host "`n🎉 所有验证都通过了！您可以安全地提交 PR。" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  有一些验证失败。请修复以下问题后再提交 PR：" -ForegroundColor Yellow
    $failedTests = $validationResults | Where-Object { -not $_.Passed }
    foreach ($test in $failedTests) {
        Write-Host "  - $($test.Test): $($test.Message)" -ForegroundColor Red
    }
}

# 显示下一步操作
Write-Host "`n=== 下一步操作 ===" -ForegroundColor Cyan

if ($allPassed) {
    Write-Host "1. 运行自动化脚本: .\SUBMIT_PR_AUTOMATION.ps1" -ForegroundColor Green
    Write-Host "   或者按照手动指南: MANUAL_SUBMIT_GUIDE.md" -ForegroundColor Green
    Write-Host ""
    Write-Host "2. 提交 PR 后，访问以下链接验证:" -ForegroundColor Green
    Write-Host "   https://github.com/symi-daguo/brands/pulls" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "3. PR 合并后，验证图标可访问性:" -ForegroundColor Green
    Write-Host "   https://brands.home-assistant.io/ha_two_way_sync/icon.png" -ForegroundColor Cyan
    Write-Host "   https://brands.home-assistant.io/ha_two_way_sync/<EMAIL>" -ForegroundColor Cyan
} else {
    Write-Host "Please fix the failed validations first, then run this script again." -ForegroundColor Yellow
}

Write-Host ""
