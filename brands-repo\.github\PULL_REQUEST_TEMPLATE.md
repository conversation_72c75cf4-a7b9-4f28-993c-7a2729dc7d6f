<!--
  You are amazing! Thanks for contributing to our project!
  Please, DO NOT DELETE ANY TEXT from this template! (unless instructed).
-->
## Proposed change
<!-- 
  Describe the big picture of your changes here to communicate to the
  maintainers why we should accept this pull request.
-->


## Type of change
<!--
  What type of change does your PR introduce to the Home Assistant Brands?
  NOTE: Please, check only 1! box! 
  If your PR requires multiple boxes to be checked, you'll most likely need to
  split it into multiple PRs. This makes things easier and faster to code review.
-->

- [ ] Add a new logo or icon for a new core integration
- [ ] Add a missing icon or logo for an existing core integration
- [ ] Add a new logo or icon for a custom integration (custom component)
  - [ ] I've added a link to my custom integration repository in the PR description
- [ ] Replace an existing icon or logo with a higher quality version
- [ ] Replace an existing icon or logo after a branding change
- [ ] Removing an icon or logo

## Additional information
<!--
  Details are important, and help maintainers processing your PR.
  Please be sure to fill out additional details, if applicable.
-->

- This PR fixes or closes issue: fixes #
- Link to code base pull request: 
- Link to documentation pull request: 
- Link to integration documentation on our website: 
- Link to custom integration repository:

## Checklist
<!--
  Put an `x` in the boxes that apply. You can also fill these out after
  creating the PR. If you're unsure about any of them, don't hesitate to ask.
  We're here to help! This is simply a reminder of what we are going to look
  for before merging your contribution.
-->

- [ ] The added/replaced image(s) are **PNG**
- [ ] Icon image size is 256x256px (`icon.png`)
- [ ] hDPI icon image size is 512x512px for  (`<EMAIL>`)
- [ ] Logo image size has min 128px, but max 256px, on the shortest side (`logo.png`)
- [ ] hDPI logo image size has min 256px, but max 512px, on the shortest side (`<EMAIL>`)

<!--
  Thank you for contributing <3
-->
