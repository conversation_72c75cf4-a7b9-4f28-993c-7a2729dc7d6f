Add-Type -AssemblyName System.Drawing

function Resize-Image {
    param(
        [string]$InputPath,
        [string]$OutputPath,
        [int]$Width,
        [int]$Height
    )
    
    # Load the original image
    $originalImage = [System.Drawing.Image]::FromFile($InputPath)
    
    # Create a new bitmap with the desired size
    $resizedImage = New-Object System.Drawing.Bitmap($Width, $Height)
    
    # Create graphics object for drawing
    $graphics = [System.Drawing.Graphics]::FromImage($resizedImage)
    
    # Set high quality settings
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
    $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
    
    # Draw the resized image
    $graphics.DrawImage($originalImage, 0, 0, $Width, $Height)
    
    # Save the resized image
    $resizedImage.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Clean up
    $graphics.Dispose()
    $resizedImage.Dispose()
    $originalImage.Dispose()
    
    Write-Host "Created: $OutputPath ($Width x $Height)"
}

# Create the required sizes for Home Assistant brands
Write-Host "Converting SYMI logo to Home Assistant brands format..."

# Create 256x256 version (icon.png)
Resize-Image -InputPath "SYMI-logo.png" -OutputPath "icon.png" -Width 256 -Height 256

# Create 512x512 version (<EMAIL>)
Resize-Image -InputPath "SYMI-logo.png" -OutputPath "<EMAIL>" -Width 512 -Height 512

Write-Host "Conversion completed successfully!"
Write-Host "Created files:"
Write-Host "  - icon.png (256x256)"
Write-Host "  - <EMAIL> (512x512)"
