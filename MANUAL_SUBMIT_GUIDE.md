# 手动提交 PR 到 home-assistant/brands 仓库

## 快速步骤总览

1. **Fork 仓库**: https://github.com/home-assistant/brands → 点击 Fork
2. **克隆到本地**: `git clone https://github.com/symi-daguo/brands.git`
3. **创建分支**: `git checkout -b add-symi-ha-two-way-sync-icons`
4. **复制文件**: 将准备好的图标文件复制到正确位置
5. **提交推送**: 提交更改并推送到您的 fork
6. **创建 PR**: 在 GitHub 上创建 Pull Request

## 详细步骤

### 1. Fork 官方仓库
- 访问: https://github.com/home-assistant/brands
- 点击右上角的 "Fork" 按钮
- 选择您的账户 (symi-daguo)

### 2. 克隆您的 Fork
```bash
git clone https://github.com/symi-daguo/brands.git
cd brands
```

### 3. 创建新分支
```bash
git checkout -b add-symi-ha-two-way-sync-icons
```

### 4. 复制文件到正确位置
在 brands 仓库根目录下：
```bash
# 创建目录
mkdir -p custom_integrations/ha_two_way_sync

# 复制文件（从您的工作目录）
cp "C:\Users\<USER>\vscode\brands\custom_integrations\ha_two_way_sync\icon.png" "custom_integrations/ha_two_way_sync/icon.png"
cp "C:\Users\<USER>\vscode\brands\custom_integrations\ha_two_way_sync\<EMAIL>" "custom_integrations/ha_two_way_sync/<EMAIL>"
```

### 5. 提交更改
```bash
# 添加文件
git add custom_integrations/ha_two_way_sync/

# 提交
git commit -m "Add SYMI 亖米 brand icons for ha_two_way_sync integration

- Add icon.png (256x256) for ha_two_way_sync integration  
- Add <EMAIL> (512x512) high-resolution version
- Icons represent SYMI 亖米 brand for Home Assistant integration
- Required for HACS validation and integration distribution"

# 推送
git push origin add-symi-ha-two-way-sync-icons
```

### 6. 创建 Pull Request
1. 访问您的 fork: https://github.com/symi-daguo/brands
2. 点击 "Compare & pull request" 按钮
3. 填写 PR 信息:
   - **标题**: `Add SYMI 亖米 brand icons for ha_two_way_sync integration`
   - **描述**: 复制 `PR_DESCRIPTION.md` 文件的完整内容

## 验证清单

提交前请确认：
- [ ] 文件路径正确: `custom_integrations/ha_two_way_sync/icon.png`
- [ ] 文件路径正确: `custom_integrations/ha_two_way_sync/<EMAIL>`
- [ ] icon.png 尺寸为 256x256
- [ ] <EMAIL> 尺寸为 512x512
- [ ] 两个文件都是 PNG 格式
- [ ] 文件大小合理 (< 50KB)
- [ ] 分支名称正确
- [ ] 提交信息清晰

## 预期结果

PR 提交后：
1. GitHub Actions 会自动运行验证
2. Home Assistant 维护者会审核您的 PR
3. 审核通过后，PR 会被合并
4. 您的图标会在 https://brands.home-assistant.io/ha_two_way_sync/ 上可用
5. HACS 验证将通过
6. 您的集成将可以在 HACS 中搜索到

## 故障排除

**如果 git push 失败**:
- 确保您已经设置了 GitHub 认证 (SSH 密钥或个人访问令牌)
- 检查网络连接

**如果文件路径错误**:
- 确保在 brands 仓库根目录下操作
- 检查目录结构是否正确

**如果 PR 被拒绝**:
- 检查图标尺寸和格式
- 确保遵循 Home Assistant 品牌指南
- 根据维护者反馈进行修改

## 联系信息

如果遇到问题，可以：
1. 查看 Home Assistant brands 仓库的 README
2. 在 PR 中询问维护者
3. 参考其他成功的 PR 示例
