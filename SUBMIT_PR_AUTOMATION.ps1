# Home Assistant Brands PR 自动提交脚本
# 用于自动化提交 SYMI 亖米 品牌图标到 home-assistant/brands 仓库

param(
    [string]$GitHubUsername = "symi-daguo",
    [string]$BrandsRepoPath = "",
    [switch]$Help
)

if ($Help) {
    Write-Host @"
Home Assistant Brands PR 自动提交脚本

用法:
    .\SUBMIT_PR_AUTOMATION.ps1 [-GitHubUsername <username>] [-BrandsRepoPath <path>]

参数:
    -GitHubUsername    您的 GitHub 用户名 (默认: symi-daguo)
    -BrandsRepoPath    brands 仓库的本地路径 (如果为空，脚本会提示您输入)
    -Help              显示此帮助信息

示例:
    .\SUBMIT_PR_AUTOMATION.ps1
    .\SUBMIT_PR_AUTOMATION.ps1 -BrandsRepoPath "C:\git\brands"
"@
    exit 0
}

Write-Host "=== Home Assistant Brands PR 自动提交脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查 git 是否安装
try {
    git --version | Out-Null
} catch {
    Write-Host "错误: 未找到 git 命令。请先安装 Git。" -ForegroundColor Red
    exit 1
}

# 获取 brands 仓库路径
if (-not $BrandsRepoPath) {
    Write-Host "请输入您的 brands 仓库本地路径:"
    Write-Host "例如: C:\git\brands 或 D:\projects\brands"
    $BrandsRepoPath = Read-Host "路径"
}

# 验证路径
if (-not (Test-Path $BrandsRepoPath)) {
    Write-Host "错误: 路径不存在: $BrandsRepoPath" -ForegroundColor Red
    Write-Host "请先按照 FORK_AND_SETUP_GUIDE.md 克隆 brands 仓库。" -ForegroundColor Yellow
    exit 1
}

# 验证是否是 git 仓库
if (-not (Test-Path (Join-Path $BrandsRepoPath ".git"))) {
    Write-Host "错误: 指定路径不是 git 仓库: $BrandsRepoPath" -ForegroundColor Red
    exit 1
}

# 切换到 brands 仓库目录
Set-Location $BrandsRepoPath
Write-Host "切换到 brands 仓库目录: $BrandsRepoPath" -ForegroundColor Green

# 检查当前仓库是否是正确的 fork
$remoteUrl = git remote get-url origin 2>$null
if ($remoteUrl -notlike "*$GitHubUsername/brands*") {
    Write-Host "警告: 当前仓库似乎不是您的 brands fork。" -ForegroundColor Yellow
    Write-Host "当前 origin: $remoteUrl" -ForegroundColor Yellow
    Write-Host "预期: https://github.com/$GitHubUsername/brands.git" -ForegroundColor Yellow
    $continue = Read-Host "是否继续? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

# 创建目标目录
$targetDir = "custom_integrations\ha_two_way_sync"
Write-Host "创建目标目录: $targetDir" -ForegroundColor Green
New-Item -ItemType Directory -Path $targetDir -Force | Out-Null

# 复制图标文件
$sourceDir = "C:\Users\<USER>\vscode\brands\custom_integrations\ha_two_way_sync"
if (Test-Path $sourceDir) {
    Write-Host "复制图标文件..." -ForegroundColor Green
    Copy-Item "$sourceDir\icon.png" "$targetDir\icon.png" -Force
    Copy-Item "$sourceDir\<EMAIL>" "$targetDir\<EMAIL>" -Force
    
    # 验证文件
    if ((Test-Path "$targetDir\icon.png") -and (Test-Path "$targetDir\<EMAIL>")) {
        Write-Host "✓ 图标文件复制成功" -ForegroundColor Green
    } else {
        Write-Host "错误: 图标文件复制失败" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "错误: 源文件目录不存在: $sourceDir" -ForegroundColor Red
    Write-Host "请确保您已经运行了图标生成脚本。" -ForegroundColor Yellow
    exit 1
}

# 创建并切换到新分支
$branchName = "add-symi-ha-two-way-sync-icons"
Write-Host "创建新分支: $branchName" -ForegroundColor Green

# 检查分支是否已存在
$existingBranch = git branch --list $branchName
if ($existingBranch) {
    Write-Host "分支 $branchName 已存在，切换到该分支..." -ForegroundColor Yellow
    git checkout $branchName
} else {
    git checkout -b $branchName
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 创建/切换分支失败" -ForegroundColor Red
    exit 1
}

# 添加文件到 git
Write-Host "添加文件到 git..." -ForegroundColor Green
git add $targetDir

# 检查是否有更改
$status = git status --porcelain
if (-not $status) {
    Write-Host "没有检测到更改。文件可能已经存在且相同。" -ForegroundColor Yellow
} else {
    # 提交更改
    $commitMessage = @"
Add SYMI 亖米 brand icons for ha_two_way_sync integration

- Add icon.png (256x256) for ha_two_way_sync integration
- Add <EMAIL> (512x512) high-resolution version
- Icons represent SYMI 亖米 brand for Home Assistant integration
- Required for HACS validation and integration distribution
"@

    Write-Host "提交更改..." -ForegroundColor Green
    git commit -m $commitMessage

    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 提交失败" -ForegroundColor Red
        exit 1
    }

    # 推送到远程仓库
    Write-Host "推送到远程仓库..." -ForegroundColor Green
    git push origin $branchName

    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 推送失败" -ForegroundColor Red
        Write-Host "请检查您的 GitHub 认证设置。" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host ""
Write-Host "=== 成功! ===" -ForegroundColor Green
Write-Host "文件已成功推送到您的 fork。" -ForegroundColor Green
Write-Host ""
Write-Host "下一步:" -ForegroundColor Yellow
Write-Host "1. 访问: https://github.com/$GitHubUsername/brands" -ForegroundColor Cyan
Write-Host "2. 点击 'Compare & pull request' 按钮" -ForegroundColor Cyan
Write-Host "3. 使用 PR_DESCRIPTION.md 中的内容作为 PR 描述" -ForegroundColor Cyan
Write-Host "4. 提交 Pull Request" -ForegroundColor Cyan
Write-Host ""
Write-Host "PR 标题建议: Add SYMI 亖米 brand icons for ha_two_way_sync integration" -ForegroundColor Green
