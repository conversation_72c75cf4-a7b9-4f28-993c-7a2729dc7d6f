Add-Type -AssemblyName System.Drawing

function Resize-Image {
    param(
        [string]$InputPath,
        [string]$OutputPath,
        [int]$Width,
        [int]$Height
    )
    
    $originalImage = [System.Drawing.Image]::FromFile($InputPath)
    $resizedImage = New-Object System.Drawing.Bitmap($Width, $Height)
    $graphics = [System.Drawing.Graphics]::FromImage($resizedImage)
    
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
    $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
    
    $graphics.DrawImage($originalImage, 0, 0, $Width, $Height)
    $resizedImage.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    $graphics.Dispose()
    $resizedImage.Dispose()
    $originalImage.Dispose()
    
    Write-Host "Created: $OutputPath ($Width x $Height)"
}

Write-Host "Creating Home Assistant standard icons for SYMI brand..."

# Create 256x256 version (icon.png)
Resize-Image -InputPath "SYMI-logo.png" -OutputPath "icon.png" -Width 256 -Height 256

# Create 512x512 version (<EMAIL>)
Resize-Image -InputPath "SYMI-logo.png" -OutputPath "<EMAIL>" -Width 512 -Height 512

Write-Host "Icons created successfully!"
