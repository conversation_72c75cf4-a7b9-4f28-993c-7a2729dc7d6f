name: Compress

# on:
#   pull_request:
#     paths:
#       - "src/**.png"
on:
  push:
    branches-ignore:
      - "**"

jobs:
  compress:
    name: Images
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5.0.0
      - name: Find symlinked paths to ignore
        id: find
        run: >-
          echo "::set-output name=ignore::$(find -L src -xtype l -type d | sed -e 's#$#/**#' | head -c -1 | tr '\n' ',')"
      - name: Check ignored paths
        run: echo ${{ steps.find.outputs.ignore }} | tr ',' '\n'
      - name: Compress Images
        uses: calibreapp/image-actions@master
        with:
          ignorePaths: ${{ steps.find.outputs.ignore }}
          githubToken: ${{ secrets.GITHUB_TOKEN }}
