[build]
  publish = "build"
  command = "bash ./scripts/build.sh"

[[headers]]
  for = "/*"
  [headers.values]
    Cache-Control = "public, max-age=604800, s-maxage=86400, stale-while-revalidate=604800"
    Access-Control-Allow-Origin = "*"
    Timing-Allow-Origin = "*"

[[redirects]]
  from = "/"
  to = "https://github.com/home-assistant/brands"
  force = true
  status = 301

[[redirects]]
  from = "/_/*/:image"
  to = "/_/_placeholder/:image"
  force = false
  status = 200

[[redirects]]
  from = "/brands/_/*/:image"
  to = "/brands/_/_placeholder/:image"
  force = false
  status = 200
