Add-Type -AssemblyName System.Drawing

Write-Host "=== Home Assistant Brands Validation ===" -ForegroundColor Green
Write-Host ""

# Check icon.png
$iconPath = "custom_integrations\ha_two_way_sync\icon.png"
if (Test-Path $iconPath) {
    $img = [System.Drawing.Image]::FromFile($iconPath)
    Write-Host "icon.png - Width: $($img.Width), Height: $($img.Height)" -ForegroundColor Green
    $img.Dispose()
} else {
    Write-Host "icon.png - NOT FOUND" -ForegroundColor Red
}

# Check <EMAIL>
$icon2xPath = "custom_integrations\ha_two_way_sync\<EMAIL>"
if (Test-Path $icon2xPath) {
    $img = [System.Drawing.Image]::FromFile($icon2xPath)
    Write-Host "<EMAIL> - Width: $($img.Width), Height: $($img.Height)" -ForegroundColor Green
    $img.Dispose()
} else {
    Write-Host "<EMAIL> - NOT FOUND" -ForegroundColor Red
}

# Check directory structure
if (Test-Path "custom_integrations\ha_two_way_sync") {
    Write-Host "Directory structure - OK" -ForegroundColor Green
} else {
    Write-Host "Directory structure - MISSING" -ForegroundColor Red
}

# Check PR files
if (Test-Path "PR_DESCRIPTION.md") {
    Write-Host "PR_DESCRIPTION.md - OK" -ForegroundColor Green
} else {
    Write-Host "PR_DESCRIPTION.md - MISSING" -ForegroundColor Red
}

Write-Host ""
Write-Host "Validation complete!" -ForegroundColor Green
