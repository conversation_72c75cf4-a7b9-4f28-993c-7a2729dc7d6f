# Fork 和设置 home-assistant/brands 仓库指南

## 步骤 1: Fork home-assistant/brands 仓库

1. **访问官方仓库**
   - 打开浏览器，访问: https://github.com/home-assistant/brands
   
2. **Fork 仓库**
   - 点击页面右上角的 "Fork" 按钮
   - 选择您的 GitHub 账户 (symi-daguo)
   - 等待 fork 完成

3. **验证 Fork**
   - Fork 完成后，您将被重定向到: https://github.com/symi-daguo/brands
   - 确认仓库名称显示为 "symi-daguo/brands"

## 步骤 2: 克隆您的 Fork 到本地

```bash
# 克隆您的 fork 到本地
git clone https://github.com/symi-daguo/brands.git
cd brands

# 添加上游仓库（可选，用于保持同步）
git remote add upstream https://github.com/home-assistant/brands.git
```

## 步骤 3: 创建新分支

```bash
# 创建并切换到新分支
git checkout -b add-symi-ha-two-way-sync-icons

# 验证当前分支
git branch
```

## 步骤 4: 复制准备好的文件

将我们准备好的文件复制到正确位置：

```bash
# 在 brands 仓库根目录下，创建目标目录
mkdir -p custom_integrations/ha_two_way_sync

# 从您的工作目录复制文件
# 假设您的工作目录是 C:\Users\<USER>\vscode\brands
copy "C:\Users\<USER>\vscode\brands\custom_integrations\ha_two_way_sync\icon.png" "custom_integrations\ha_two_way_sync\icon.png"
copy "C:\Users\<USER>\vscode\brands\custom_integrations\ha_two_way_sync\<EMAIL>" "custom_integrations\ha_two_way_sync\<EMAIL>"
```

## 步骤 5: 验证文件结构

确认文件结构正确：

```
brands/
└── custom_integrations/
    └── ha_two_way_sync/
        ├── icon.png      (256x256)
        └── <EMAIL>   (512x512)
```

## 步骤 6: 提交更改

```bash
# 添加文件到 git
git add custom_integrations/ha_two_way_sync/

# 提交更改
git commit -m "Add SYMI 亖米 brand icons for ha_two_way_sync integration

- Add icon.png (256x256) for ha_two_way_sync integration
- Add <EMAIL> (512x512) high-resolution version
- Icons represent SYMI 亖米 brand for Home Assistant integration
- Required for HACS validation and integration distribution"

# 推送到您的 fork
git push origin add-symi-ha-two-way-sync-icons
```

## 步骤 7: 创建 Pull Request

1. **访问您的 Fork**
   - 打开: https://github.com/symi-daguo/brands
   
2. **创建 PR**
   - 点击 "Compare & pull request" 按钮
   - 或者点击 "Pull requests" 标签，然后点击 "New pull request"
   
3. **设置 PR 详情**
   - **Base repository**: home-assistant/brands
   - **Base branch**: master
   - **Head repository**: symi-daguo/brands  
   - **Compare branch**: add-symi-ha-two-way-sync-icons
   
4. **填写 PR 信息**
   - **Title**: `Add SYMI 亖米 brand icons for ha_two_way_sync integration`
   - **Description**: 复制 `PR_DESCRIPTION.md` 文件的内容

## 步骤 8: 等待审核

1. **自动检查**
   - GitHub Actions 将自动运行检查
   - 确保所有检查都通过
   
2. **人工审核**
   - Home Assistant 维护者将审核您的 PR
   - 可能会要求修改或提供额外信息
   
3. **合并**
   - 审核通过后，PR 将被合并到主分支
   - 您的图标将在几分钟内在 brands.home-assistant.io 上可用

## 重要提示

- 确保图标文件大小合理（通常 < 50KB）
- 图标应该在浅色和深色背景上都清晰可见
- 遵循 Home Assistant 的品牌指南
- 保持耐心，审核过程可能需要几天时间

## 故障排除

如果遇到问题：
1. 检查文件路径是否正确
2. 确认图标尺寸符合要求
3. 验证 git 命令执行无误
4. 查看 GitHub Actions 的错误信息
