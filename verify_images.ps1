Add-Type -AssemblyName System.Drawing

function Check-Image {
    param([string]$Path)
    
    if (Test-Path $Path) {
        $img = [System.Drawing.Image]::FromFile($Path)
        Write-Host "$Path - Width: $($img.Width), Height: $($img.Height), Format: $($img.PixelFormat)"
        $img.Dispose()
    } else {
        Write-Host "$Path - File not found!"
    }
}

Write-Host "Verifying generated images:"
Check-Image "icon.png"
Check-Image "<EMAIL>"

# Also check file sizes
Write-Host "`nFile sizes:"
Get-ChildItem "icon.png", "<EMAIL>" | ForEach-Object {
    Write-Host "$($_.Name) - $([math]::Round($_.Length / 1KB, 2)) KB"
}
