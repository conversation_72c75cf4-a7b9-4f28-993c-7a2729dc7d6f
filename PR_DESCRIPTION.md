# Add SYMI 亖米 brand icons for ha_two_way_sync integration

## Summary
This PR adds brand icons for the `ha_two_way_sync` custom integration to the Home Assistant brands repository.

## Integration Details
- **Integration Domain**: `ha_two_way_sync`
- **Integration Name**: Home Assistant SYMI双向同步
- **Brand**: SYMI 亖米
- **Developer**: @symi-daguo (GitHub: https://github.com/symi-daguo)
- **Integration Repository**: https://github.com/symi-daguo/ha_two_way_sync

## Brand Information
- **Brand Name**: SYMI 亖米
- **Brand Description**: SYMI is a technology brand focused on smart home automation and IoT solutions
- **Logo Design**: Modern minimalist design representing connectivity and synchronization

## Files Added
```
custom_integrations/ha_two_way_sync/
├── icon.png      # 256x256 PNG icon
└── <EMAIL>   # 512x512 PNG high-resolution icon
```

## Icon Specifications
- ✅ **Format**: PNG with transparency support
- ✅ **Sizes**: 256x256 (icon.png) and 512x512 (<EMAIL>)
- ✅ **Aspect Ratio**: 1:1 (square)
- ✅ **Optimization**: Lossless compression applied
- ✅ **Background**: Optimized for both light and dark themes
- ✅ **Quality**: High-resolution vector-based design scaled appropriately

## Integration Status
- ✅ Integration is publicly available on GitHub
- ✅ Integration follows Home Assistant development guidelines
- ✅ Integration has proper manifest.json with all required fields
- ✅ Integration is ready for HACS distribution

## Purpose
This PR enables the `ha_two_way_sync` integration to:
1. Pass HACS validation requirements
2. Display proper brand icons in Home Assistant UI
3. Be discoverable in the Home Assistant Community Store (HACS)
4. Provide a professional user experience with branded visuals

## Testing
- [x] Icons display correctly in Home Assistant frontend
- [x] Icons are properly sized and formatted
- [x] Icons work well on both light and dark backgrounds
- [x] File sizes are optimized for web delivery

## Related Links
- Integration Repository: https://github.com/symi-daguo/ha_two_way_sync
- Developer Profile: https://github.com/symi-daguo
- HACS Integration: Will be available after this PR is merged

---

**Note**: This PR is required for HACS validation and integration distribution. The integration is fully functional and ready for community use.
