# Add SYMI 亖米 brand icons

## Summary
This PR adds brand icons for the SYMI 亖米 brand to the Home Assistant brands repository. These icons can be used by all SYMI integrations.

## Brand Details
- **Brand Name**: SYMI 亖米
- **Brand Domain**: `symi`
- **Developer**: @symi-daguo (GitHub: https://github.com/symi-daguo)
- **Brand Website**: https://github.com/symi-daguo

## Brand Information
- **Brand Name**: SYMI 亖米
- **Brand Description**: SYMI is a technology brand focused on smart home automation and IoT solutions
- **Logo Design**: Modern minimalist design representing connectivity and synchronization
- **Usage**: Can be referenced by all SYMI integrations using brand: "symi"

## Files Added
```
core_brands/symi/
├── icon.png      # 256x256 PNG icon
└── <EMAIL>   # 512x512 PNG high-resolution icon
```

## Icon Specifications
- ✅ **Format**: PNG with transparency support
- ✅ **Sizes**: 256x256 (icon.png) and 512x512 (<EMAIL>)
- ✅ **Aspect Ratio**: 1:1 (square)
- ✅ **Optimization**: Lossless compression applied
- ✅ **Background**: Optimized for both light and dark themes
- ✅ **Quality**: High-resolution vector-based design scaled appropriately

## Integration Status
- ✅ Integration is publicly available on GitHub
- ✅ Integration follows Home Assistant development guidelines
- ✅ Integration has proper manifest.json with all required fields
- ✅ Integration is ready for HACS distribution

## Purpose
This PR enables all SYMI integrations to:
1. Use consistent brand icons across all integrations
2. Display proper brand icons in Home Assistant UI
3. Pass HACS validation requirements
4. Provide a professional user experience with unified branding

## Testing
- [x] Icons display correctly in Home Assistant frontend
- [x] Icons are properly sized and formatted
- [x] Icons work well on both light and dark backgrounds
- [x] File sizes are optimized for web delivery

## Related Links
- Developer Profile: https://github.com/symi-daguo
- SYMI Brand: Will be available at https://brands.home-assistant.io/symi/ after merge

## Integration Usage
Integrations can reference this brand by adding to their manifest.json:
```json
{
  "brand": "symi"
}
```

---

**Note**: This PR adds a reusable brand that can be used by all SYMI integrations for consistent branding across the Home Assistant ecosystem.
